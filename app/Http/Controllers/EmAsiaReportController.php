<?php

namespace App\Http\Controllers;

use App\Services\EmAsiaReportHelper;
use App\Services\ExcelHelper;
use App\Services\TimezoneHelper;
use Illuminate\Http\Request;
use Illuminate\Support\Str;

class EmAsiaReportController extends Controller
{
    public function index()
    {
        $tz_helper = new TimezoneHelper();
        $timezone_list = $tz_helper->listWithTime();

        $em_asia_helper = new EmAsiaReportHelper();
        $country_list = $em_asia_helper->countryList();

        return view('em_asia_report.index', compact('timezone_list', 'country_list'));
    }

    public function process(Request $request)
    {
        $request->validate([
            'vendor' => 'required',
            'webinar_owner_country' => 'required',
            'webinar_name' => 'required',
            'webinar_date_time' => 'required',
            'timezone_offset' => 'required',
            'webinar_duration' => 'required',
            'webinar_owner_name' => 'required',
            'webinar_owner_email_address' => 'required',
            'brand' => 'required',
            'category' => 'required',
            'list_of_registered' => 'required',
            'list_of_attended' => 'required',
        ]);

        try {
            $brand_list = array_values($request->get('brand'));
            $category_list = array_values($request->get('category'));
            $bc_list = [];
            for ($i = 0; $i < count($brand_list); $i++) {
                $bc_list[$i + 1] = [
                    'brand' => $brand_list[$i],
                    'category' => $category_list[$i],
                ];
            }
            $emAsiaReport = new EmAsiaReportHelper();
            $registered_report = $emAsiaReport->listOfRegisteredReport(
                $request->get('vendor'),
                $request->get('webinar_owner_country'),
                $request->get('webinar_name'),
                $request->get('webinar_date_time'),
                $request->get('timezone_offset'),
                $request->get('webinar_duration'),
                $request->get('webinar_owner_name'),
                $request->get('webinar_owner_email_address'),
                $bc_list,
                $request->file('list_of_registered')
            );
            $attended_report = $emAsiaReport->listOfAttendedReport(
                $request->get('vendor'),
                $request->get('webinar_owner_country'),
                $request->get('webinar_name'),
                $request->get('webinar_date_time'),
                $request->get('timezone_offset'),
                $request->get('webinar_duration'),
                $request->get('webinar_owner_name'),
                $request->get('webinar_owner_email_address'),
                $bc_list,
                $request->file('list_of_attended'),
                $registered_report
            );
            $register_emails = array_column($registered_report, '*HCP Email Address');
            foreach ($attended_report as $i => $k) {
                $email = $k['*HCP Email Address'];
                $search = in_array($email, $register_emails);
                if ($search === false) {
                    unset($attended_report[$i]);
                }
            }
            $attended_report = array_values($attended_report);

            $date = date_parse_from_format('d/m/Y H:i', $request->get('webinar_date_time'));
            $date = strtotime($date['year'] . '-' . $date['month'] . '-' . $date['day'] . ' ' . $date['hour'] . ':' . $date['minute'] . ':' . $date['second']);
            $session = $request->get('session') ?? 1;

            $excel_helper = new ExcelHelper();
            $excel_helper->setFilename(implode('_', [
                'VISTREAM',
                '(' . $request->get('webinar_owner_country') . ')',
                'WEBINAR',
                '(' . date('Ymd', $date) . ')',
                '(' . ($session < 10 ? 0 : null) . $session . ')',
                '(' . strtoupper(Str::slug($request->get('webinar_name'), '_')) . ')'
            ]));
        } catch (\Exception $e) {
            return redirect()->back()->with('error', $e->getMessage());
        }

        return $excel_helper->createExcel(
            [
                'title' => 'List of Registered',
                'data' => $registered_report,
                'options' => [
                    'BM' => 'TEXT',
                    'CB' => 'TEXT',
                ]
            ],
            [
                'title' => 'List of Attended',
                'data' => $attended_report,
                'options' => [
                    'F' => 'TIME',
                    'J' => 'TEXT',
                ]
            ]
        );
    }

    public function brands(Request $request)
    {
        $search = $request->get('search');
        $country = $request->get('country');
        $brandByCountry = json_decode(file_get_contents(public_path('related/em-asia-report/brandByCountry.json')), true);
        if ($country) {
            $brandByCountry = array_filter($brandByCountry, function ($item) use ($country) {
                return $item['country_code'] == $country;
            });
        }
        if ($search) {
            $brandByCountry = array_filter($brandByCountry, function ($item) use ($search) {
                return str_contains(strtolower($item['brand_description']), strtolower($search));
            });
        }
        $result = array_map(function ($item) {
            return [
                'id' => $item['brand_id'] . '-' . $item['brand_description'],
                'text' => $item['brand_description']
            ];
        }, $brandByCountry);
        $result = array_values($result);

        if (!empty($search) or (!empty($country) and $country != 'Select a Country')) {
            $result[] = [
                'id' => '0-OTHERS',
                'text' => 'OTHERS'
            ];
        }

        return response()->json($result);
    }
}
